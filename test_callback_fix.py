#!/usr/bin/env python3
"""
Тестовый файл для проверки исправления проблем с callback и logger
"""

def test_logger_import():
    """Проверяем, что logger правильно импортирован"""
    
    print("🧪 Тестирование импорта logger...")
    
    try:
        # Проверяем, что logger определен в модуле
        from common.tests_statistics.register_handlers import logger
        print("✅ Logger успешно импортирован")
        
        # Проверяем, что это действительно logger
        import logging
        assert isinstance(logger, logging.Logger)
        print("✅ Logger является корректным объектом logging.Logger")
        
        return True
        
    except ImportError as e:
        print(f"❌ Ошибка импорта logger: {e}")
        return False
    except Exception as e:
        print(f"❌ Другая ошибка: {e}")
        return False

def test_navigation_functions():
    """Проверяем, что функции навигации не содержат ошибок"""
    
    print("\n🧪 Тестирование функций навигации...")
    
    try:
        # Проверяем, что функции можно импортировать
        from common.tests_statistics.register_handlers import (
            handle_ent_history_back_to_result,
            handle_ent_history_back_to_list,
            handle_ent_history_back_to_detail,
            handle_ent_history_back_to_subjects,
            handle_ent_history_back_to_menu
        )
        print("✅ Функции навигации успешно импортированы")
        
        # Проверяем, что функции являются корутинами
        import inspect
        assert inspect.iscoroutinefunction(handle_ent_history_back_to_result)
        assert inspect.iscoroutinefunction(handle_ent_history_back_to_list)
        assert inspect.iscoroutinefunction(handle_ent_history_back_to_detail)
        assert inspect.iscoroutinefunction(handle_ent_history_back_to_subjects)
        assert inspect.iscoroutinefunction(handle_ent_history_back_to_menu)
        print("✅ Функции навигации являются корректными async функциями")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка при проверке функций навигации: {e}")
        return False

def test_callback_creation():
    """Проверяем, что создание CallbackQuery работает корректно"""
    
    print("\n🧪 Тестирование создания CallbackQuery...")
    
    try:
        from aiogram.types import CallbackQuery, User, Message, Chat
        
        # Создаем тестовые объекты
        user = User(id=123, is_bot=False, first_name="Test")
        chat = Chat(id=456, type="private")
        message = Message(message_id=789, date=1234567890, chat=chat)
        
        # Создаем CallbackQuery
        callback = CallbackQuery(
            id="test_id",
            from_user=user,
            message=message,
            data="test_data",
            chat_instance="test_instance"
        )
        
        print("✅ CallbackQuery успешно создан")
        
        # Проверяем, что данные корректны
        assert callback.data == "test_data"
        assert callback.from_user.id == 123
        print("✅ Данные CallbackQuery корректны")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка при создании CallbackQuery: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Тестирование исправления проблем с callback и logger...")
    
    success = True
    
    if not test_logger_import():
        success = False
    
    if not test_navigation_functions():
        success = False
    
    if not test_callback_creation():
        success = False
    
    if success:
        print("\n🎉 Все тесты прошли успешно!")
        print("\n📋 Исправленные проблемы:")
        print("   ✅ Добавлен импорт logger в register_handlers.py")
        print("   ✅ Исправлено создание новых CallbackQuery объектов")
        print("   ✅ Убрана попытка изменения frozen объектов")
        print("   ✅ Функции навигации теперь работают корректно")
        print("\n🚀 Теперь навигация должна работать без ошибок!")
    else:
        print("\n❌ Некоторые тесты не прошли. Проверьте исправления.")
