#!/usr/bin/env python3
"""
Тестовый файл для проверки правильной регистрации обработчиков
"""

def test_ent_result_handler_registration():
    """Проверяем, что для состояния ent_result зарегистрирован правильный обработчик"""
    
    print("🧪 Тестирование регистрации обработчика для ent_result...")
    
    try:
        # Проверяем обработчики для куратора
        from curator.states.states_tests import STATE_HANDLERS, CuratorTestsStatisticsStates
        from common.tests_statistics.register_handlers import handle_ent_history_back_to_result
        
        # Проверяем, что состояние ent_result существует
        assert hasattr(CuratorTestsStatisticsStates, 'ent_result')
        print("✅ Состояние ent_result существует")
        
        # Проверяем, что для ent_result зарегистрирован обработчик
        assert CuratorTestsStatisticsStates.ent_result in STATE_HANDLERS
        print("✅ Для ent_result зарегистрирован обработчик")
        
        # Получаем зарегистрированный обработчик
        handler = STATE_HANDLERS[CuratorTestsStatisticsStates.ent_result]
        
        # Проверяем, что это callable
        assert callable(handler)
        print("✅ Обработчик является вызываемым")
        
        # Проверяем, что это lambda функция
        assert handler.__name__ == '<lambda>'
        print("✅ Обработчик является lambda функцией")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка при проверке регистрации обработчика: {e}")
        return False

def test_handler_function_exists():
    """Проверяем, что функция handle_ent_history_back_to_result существует и корректна"""
    
    print("\n🧪 Тестирование функции handle_ent_history_back_to_result...")
    
    try:
        # Проверяем, что функцию можно импортировать
        from common.tests_statistics.register_handlers import handle_ent_history_back_to_result
        print("✅ Функция handle_ent_history_back_to_result успешно импортирована")
        
        # Проверяем, что это корутина
        import inspect
        assert inspect.iscoroutinefunction(handle_ent_history_back_to_result)
        print("✅ Функция является корректной async функцией")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка при проверке функции: {e}")
        return False

def test_source_code_changes():
    """Проверяем изменения в исходном коде"""
    
    print("\n🧪 Тестирование изменений в исходном коде...")
    
    try:
        # Читаем файл с регистрацией обработчиков
        with open("common/tests_statistics/register_handlers.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # Проверяем, что старый обработчик заменен
        old_pattern = "states_group.ent_result: lambda callback, state, user_role=None: show_ent_statistics(callback, state)"
        new_pattern = "states_group.ent_result: lambda callback, state, user_role=None: handle_ent_history_back_to_result(callback, state)"
        
        if old_pattern in content:
            print(f"❌ Найден старый обработчик: {old_pattern[:50]}...")
            return False
        
        if new_pattern in content:
            print(f"✅ Найден новый обработчик: {new_pattern[:50]}...")
        else:
            print(f"❌ НЕ найден новый обработчик: {new_pattern[:50]}...")
            return False
        
        print("✅ Обработчик для ent_result правильно заменен")
        return True
        
    except Exception as e:
        print(f"❌ Ошибка при проверке исходного кода: {e}")
        return False

def test_other_handlers_unchanged():
    """Проверяем, что другие обработчики не изменились"""
    
    print("\n🧪 Тестирование неизменности других обработчиков...")
    
    try:
        # Читаем файл с регистрацией обработчиков
        with open("common/tests_statistics/register_handlers.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # Проверяем, что другие обработчики остались без изменений
        unchanged_patterns = [
            "states_group.ent_select_group: lambda callback, state, user_role=None: show_ent_groups(callback, state)",
            "states_group.ent_select_student: lambda callback, state, user_role=None: show_ent_students(callback, state)",
            "states_group.ent_result_display: lambda callback, state, user_role=None: show_ent_statistics(callback, state)"
        ]
        
        for pattern in unchanged_patterns:
            if pattern in content:
                print(f"✅ Обработчик не изменен: {pattern[:50]}...")
            else:
                print(f"❌ Обработчик изменен: {pattern[:50]}...")
                return False
        
        print("✅ Другие обработчики остались без изменений")
        return True
        
    except Exception as e:
        print(f"❌ Ошибка при проверке других обработчиков: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Тестирование правильной регистрации обработчиков...")
    
    success = True
    
    if not test_ent_result_handler_registration():
        success = False
    
    if not test_handler_function_exists():
        success = False
    
    if not test_source_code_changes():
        success = False
    
    if not test_other_handlers_unchanged():
        success = False
    
    if success:
        print("\n🎉 Все тесты прошли успешно!")
        print("\n📋 Исправленные проблемы:")
        print("   ✅ Обработчик для ent_result заменен на handle_ent_history_back_to_result")
        print("   ✅ Теперь при переходе к ent_result вызывается правильная функция навигации")
        print("   ✅ Функция навигации будет логировать данные и вызывать show_trial_ent_student_detail")
        print("   ✅ Другие обработчики остались без изменений")
        print("\n🔧 Как это работает:")
        print("   1. Пользователь нажимает 'Назад' из состояния ent_history")
        print("   2. Система переходит к состоянию ent_result")
        print("   3. Вызывается handle_ent_history_back_to_result вместо show_ent_statistics")
        print("   4. Функция логирует данные и вызывает show_trial_ent_student_detail напрямую")
        print("   5. Никаких ошибок парсинга callback_data='back'")
        print("\n🚀 Теперь логирование будет работать и ошибки будут видны!")
    else:
        print("\n❌ Некоторые тесты не прошли. Проверьте регистрацию обработчиков.")
