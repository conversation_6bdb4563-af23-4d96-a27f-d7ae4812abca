#!/usr/bin/env python3
"""
Тестовый файл для проверки исправления ошибки с current_state
"""

def test_functions_syntax():
    """Проверяем, что функции не содержат синтаксических ошибок"""
    
    print("🧪 Тестирование синтаксиса функций...")
    
    try:
        # Проверяем, что функции можно импортировать без ошибок
        from common.tests_statistics.handlers import (
            show_ent_student_history,
            show_ent_history_detail,
            show_ent_history_analytics_subjects
        )
        print("✅ Функции show_ent_student_history и show_ent_history_detail импортированы без ошибок")
        
        # Проверяем, что функции являются корутинами
        import inspect
        assert inspect.iscoroutinefunction(show_ent_student_history)
        assert inspect.iscoroutinefunction(show_ent_history_detail)
        assert inspect.iscoroutinefunction(show_ent_history_analytics_subjects)
        print("✅ Функции являются корректными async функциями")
        
        return True
        
    except SyntaxError as e:
        print(f"❌ Синтаксическая ошибка: {e}")
        return False
    except ImportError as e:
        print(f"❌ Ошибка импорта: {e}")
        return False
    except Exception as e:
        print(f"❌ Другая ошибка: {e}")
        return False

def test_variable_usage():
    """Проверяем, что переменные используются корректно"""
    
    print("\n🧪 Тестирование использования переменных...")
    
    try:
        # Читаем содержимое файла для проверки
        with open("common/tests_statistics/handlers.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # Проверяем, что current_state определяется до использования
        lines = content.split('\n')
        
        # Ищем функцию show_ent_student_history
        in_function = False
        current_state_defined = False
        current_state_used = False
        
        for i, line in enumerate(lines):
            if "async def show_ent_student_history" in line:
                in_function = True
                current_state_defined = False
                current_state_used = False
                continue
            
            if in_function:
                if "async def " in line and "show_ent_student_history" not in line:
                    # Вышли из функции
                    break
                
                if "current_state = await state.get_state()" in line:
                    current_state_defined = True
                
                if "current_state.split(" in line and not current_state_defined:
                    print(f"❌ current_state используется до определения в строке {i+1}")
                    return False
        
        print("✅ Переменная current_state используется корректно")
        return True
        
    except Exception as e:
        print(f"❌ Ошибка при проверке: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Тестирование исправления ошибки с current_state...")
    
    success = True
    
    if not test_functions_syntax():
        success = False
    
    if not test_variable_usage():
        success = False
    
    if success:
        print("\n🎉 Все тесты прошли успешно!")
        print("\n📋 Исправленные проблемы:")
        print("   ✅ Переменная current_state теперь определяется в начале функций")
        print("   ✅ Убрано дублирующее получение current_state")
        print("   ✅ Функции не содержат синтаксических ошибок")
        print("\n🚀 Теперь история тестов должна работать без ошибок!")
    else:
        print("\n❌ Некоторые тесты не прошли. Проверьте исправления.")
