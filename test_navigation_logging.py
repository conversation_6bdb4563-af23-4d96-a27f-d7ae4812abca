#!/usr/bin/env python3
"""
Тестовый файл для проверки логирования в функциях навигации
"""

def test_navigation_functions_exist():
    """Проверяем, что функции навигации существуют и содержат логирование"""
    
    print("🧪 Тестирование функций навигации с логированием...")
    
    try:
        # Проверяем, что функции можно импортировать
        from common.tests_statistics.register_handlers import (
            handle_ent_history_back_to_result,
            handle_ent_history_back_to_list,
            handle_ent_history_back_to_subjects
        )
        print("✅ Функции навигации успешно импортированы")
        
        # Проверяем, что функции являются корутинами
        import inspect
        assert inspect.iscoroutinefunction(handle_ent_history_back_to_result)
        assert inspect.iscoroutinefunction(handle_ent_history_back_to_list)
        assert inspect.iscoroutinefunction(handle_ent_history_back_to_subjects)
        print("✅ Функции навигации являются корректными async функциями")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка при проверке функций навигации: {e}")
        return False

def test_logging_in_source_code():
    """Проверяем, что в исходном коде есть логирование"""
    
    print("\n🧪 Тестирование наличия логирования в исходном коде...")
    
    try:
        # Читаем файл с функциями навигации
        with open("common/tests_statistics/register_handlers.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # Проверяем наличие логирования
        logging_patterns = [
            "logger.error(f\"🔍 НАВИГАЦИЯ НАЗАД",
            "logger.error(f\"🔍 ВСЕ ДАННЫЕ СОСТОЯНИЯ:",
            "logger.error(f\"❌ КРИТИЧЕСКАЯ ОШИБКА",
            "logger.error(f\"❌ ТИП ОШИБКИ:",
            "exc_info=True"
        ]
        
        for pattern in logging_patterns:
            if pattern in content:
                print(f"✅ Найден паттерн логирования: {pattern[:50]}...")
            else:
                print(f"❌ НЕ найден паттерн логирования: {pattern[:50]}...")
                return False
        
        print("✅ Все паттерны логирования найдены в исходном коде")
        return True
        
    except Exception as e:
        print(f"❌ Ошибка при проверке исходного кода: {e}")
        return False

def test_error_message_format():
    """Проверяем формат сообщений об ошибках"""
    
    print("\n🧪 Тестирование формата сообщений об ошибках...")
    
    try:
        # Проверяем, что сообщения об ошибках содержат детали
        test_error = Exception("Тестовая ошибка")
        error_message = f"❌ Ошибка: {str(test_error)}"
        
        assert "❌" in error_message
        assert "Тестовая ошибка" in error_message
        print("✅ Формат сообщений об ошибках корректный")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка при проверке формата сообщений: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Тестирование логирования в функциях навигации...")
    
    success = True
    
    if not test_navigation_functions_exist():
        success = False
    
    if not test_logging_in_source_code():
        success = False
    
    if not test_error_message_format():
        success = False
    
    if success:
        print("\n🎉 Все тесты прошли успешно!")
        print("\n📋 Добавленное логирование:")
        print("   ✅ Подробное логирование данных состояния")
        print("   ✅ Логирование текущего состояния FSM")
        print("   ✅ Логирование создаваемых callback_data")
        print("   ✅ Детальное логирование ошибок с трейсбеком")
        print("   ✅ Отображение типа ошибки")
        print("   ✅ Показ ошибки пользователю в сообщении")
        print("\n🔍 Теперь в логах будет видно:")
        print("   • Какие данные есть в состоянии")
        print("   • В каком состоянии находится пользователь")
        print("   • Какие callback_data создаются")
        print("   • Полный трейсбек ошибок")
        print("\n🚀 Перезапустите бота и попробуйте навигацию - теперь ошибки будут видны!")
    else:
        print("\n❌ Некоторые тесты не прошли. Проверьте логирование.")
