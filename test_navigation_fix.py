#!/usr/bin/env python3
"""
Тестовый файл для проверки исправления навигации в истории ЕНТ
"""

def test_state_transitions():
    """Проверяем, что переходы состояний правильно настроены"""
    
    print("🧪 Тестирование переходов состояний...")
    
    try:
        # Проверяем переходы для куратора
        from curator.states.states_tests import STATE_TRANSITIONS
        
        # Проверяем, что переходы для истории ЕНТ добавлены
        from curator.states.states_tests import CuratorTestsStatisticsStates
        
        # Проверяем основные переходы
        assert CuratorTestsStatisticsStates.ent_result in STATE_TRANSITIONS
        print("✅ Основные переходы ЕНТ найдены")
        
        # Проверяем переходы истории ЕНТ (если состояния существуют)
        if hasattr(CuratorTestsStatisticsStates, 'ent_history'):
            assert CuratorTestsStatisticsStates.ent_history in STATE_TRANSITIONS
            assert CuratorTestsStatisticsStates.ent_history_detail in STATE_TRANSITIONS
            assert CuratorTestsStatisticsStates.ent_history_analytics_subjects in STATE_TRANSITIONS
            assert CuratorTestsStatisticsStates.ent_history_subject_analytics_menu in STATE_TRANSITIONS
            assert CuratorTestsStatisticsStates.ent_history_subject_analytics_detailed in STATE_TRANSITIONS
            assert CuratorTestsStatisticsStates.ent_history_subject_analytics_summary in STATE_TRANSITIONS
            print("✅ Переходы истории ЕНТ добавлены")
            
            # Проверяем правильность переходов
            assert STATE_TRANSITIONS[CuratorTestsStatisticsStates.ent_history] == CuratorTestsStatisticsStates.ent_result
            assert STATE_TRANSITIONS[CuratorTestsStatisticsStates.ent_history_detail] == CuratorTestsStatisticsStates.ent_history
            assert STATE_TRANSITIONS[CuratorTestsStatisticsStates.ent_history_analytics_subjects] == CuratorTestsStatisticsStates.ent_history_detail
            print("✅ Цепочка переходов правильная")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка при проверке переходов: {e}")
        return False

def test_state_handlers():
    """Проверяем, что обработчики состояний правильно настроены"""
    
    print("\n🧪 Тестирование обработчиков состояний...")
    
    try:
        # Проверяем обработчики для куратора
        from curator.states.states_tests import STATE_HANDLERS
        from curator.states.states_tests import CuratorTestsStatisticsStates
        
        # Проверяем основные обработчики
        assert CuratorTestsStatisticsStates.ent_result in STATE_HANDLERS
        print("✅ Основные обработчики ЕНТ найдены")
        
        # Проверяем обработчики истории ЕНТ (если состояния существуют)
        if hasattr(CuratorTestsStatisticsStates, 'ent_history'):
            assert CuratorTestsStatisticsStates.ent_history in STATE_HANDLERS
            assert CuratorTestsStatisticsStates.ent_history_detail in STATE_HANDLERS
            assert CuratorTestsStatisticsStates.ent_history_analytics_subjects in STATE_HANDLERS
            assert CuratorTestsStatisticsStates.ent_history_subject_analytics_menu in STATE_HANDLERS
            assert CuratorTestsStatisticsStates.ent_history_subject_analytics_detailed in STATE_HANDLERS
            assert CuratorTestsStatisticsStates.ent_history_subject_analytics_summary in STATE_HANDLERS
            print("✅ Обработчики истории ЕНТ добавлены")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка при проверке обработчиков: {e}")
        return False

def test_navigation_functions():
    """Проверяем, что функции навигации существуют"""
    
    print("\n🧪 Тестирование функций навигации...")
    
    try:
        # Проверяем, что функции навигации существуют
        from common.tests_statistics.register_handlers import (
            handle_ent_history_back_to_result,
            handle_ent_history_back_to_list,
            handle_ent_history_back_to_detail,
            handle_ent_history_back_to_subjects,
            handle_ent_history_back_to_menu
        )
        print("✅ Функции навигации найдены")
        
        # Проверяем, что функции являются корутинами
        import inspect
        assert inspect.iscoroutinefunction(handle_ent_history_back_to_result)
        assert inspect.iscoroutinefunction(handle_ent_history_back_to_list)
        assert inspect.iscoroutinefunction(handle_ent_history_back_to_detail)
        assert inspect.iscoroutinefunction(handle_ent_history_back_to_subjects)
        assert inspect.iscoroutinefunction(handle_ent_history_back_to_menu)
        print("✅ Функции навигации являются корректными async функциями")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка при проверке функций навигации: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Тестирование исправления навигации в истории ЕНТ...")
    
    success = True
    
    if not test_state_transitions():
        success = False
    
    if not test_state_handlers():
        success = False
    
    if not test_navigation_functions():
        success = False
    
    if success:
        print("\n🎉 Все тесты прошли успешно!")
        print("\n📋 Исправленные проблемы навигации:")
        print("   ✅ Добавлены переходы состояний для истории ЕНТ")
        print("   ✅ Добавлены обработчики для всех состояний истории ЕНТ")
        print("   ✅ Создана полная цепочка навигации назад")
        print("   ✅ Функции навигации корректно обрабатывают ошибки")
        print("\n🗺️ Маршрут навигации:")
        print("   Тесты → Пробный ЕНТ → Группа → Студент → История → Тест → Аналитика → Предмет → Микротемы")
        print("   ⬅️ Кнопка 'Назад' теперь работает на каждом этапе!")
        print("\n🚀 Теперь нужно перезапустить бота и протестировать!")
    else:
        print("\n❌ Некоторые тесты не прошли. Проверьте исправления.")
